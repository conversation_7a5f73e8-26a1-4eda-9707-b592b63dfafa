import request from '@/utils/request'

/**
 * 门禁管理服务
 */
export const accessControlService = {
    /**
     * 分页查询门禁列表
     * @param {Object} params 查询参数
     */
    getPage(params: any) {
        return request({
            url: '/accessControl/list',
            method: 'post',
            data: params
        })
    },

    /**
     * 新增门禁
     * @param {Object} data 门禁数据
     */
    add(data: any) {
        return request({
            url: '/accessControl',
            method: 'post',
            data: data
        })
    },

    /**
     * 编辑门禁
     * @param {Object} data 门禁数据
     */
    update(data: any) {
        return request({
            url: '/accessControl',
            method: 'put',
            data: data
        })
    },

    /**
     * 删除门禁（支持批量删除）
     * @param {Array} ids 门禁ID数组
     */
    delete(ids: number[]) {
        return request({
            url: '/accessControl',
            method: 'delete',
            data: ids
        })
    },

    /**
     * 根据ID查询门禁详情
     * @param {Number} id 门禁ID
     */
    getById(id: number) {
        return request({
            url: `/accessControl/${id}`,
            method: 'get'
        })
    },

    /**
     * 启用门禁
     * @param {Number} id 门禁ID
     */
    enable(id: number) {
        return request({
            url: '/accessControl/enable',
            method: 'post',
            params: { id }
        })
    },

    /**
     * 禁用门禁
     * @param {Number} id 门禁ID
     */
    disable(id: number) {
        return request({
            url: '/accessControl/disable',
            method: 'post',
            params: { id }
        })
    }
}

/**
 * 门禁授权记录服务
 */
export const authorizationRecordService = {
    /**
     * 分页查询门禁授权记录列表
     * @param {Object} params 查询参数
     */
    getPage(params: any) {
        return request({
            url: '/accessAuth/list',
            method: 'post',
            data: params
        })
    },

    /**
     * 新增门禁授权记录
     * @param {Object} data 授权记录数据
     */
    add(data: any) {
        return request({
            url: '/accessAuth',
            method: 'post',
            data: data
        })
    },

    /**
     * 删除门禁授权记录（包含批量删除）
     * @param {Array} ids 授权记录ID数组
     */
    delete(ids: number[]) {
        return request({
            url: '/accessAuth',
            method: 'delete',
            data: ids
        })
    },

    /**
     * 撤销门禁授权记录
     * @param {Number} id 授权记录ID
     * @param {String} revokeReason 撤销原因
     */
    revoke(id: number, revokeReason: string) {
        return request({
            url: `/accessAuth/revoke/${id}`,
            method: 'post',
            params: { revokeReason }
        })
    },

    /**
     * 设置门禁授权进出时间
     * @param {Object} params 时间设置参数
     */
    setAccessTimes(params: any) {
        return request({
            url: '/accessAuth/setAccessTimes',
            method: 'post',
            data: params
        })
    },



    /**
     * 根据ID查询授权记录详情
     * @param {Number} id 授权记录ID
     */
    getById(id: number) {
        return request({
            url: `/accessAuth/${id}`,
            method: 'get'
        })
    }
}
