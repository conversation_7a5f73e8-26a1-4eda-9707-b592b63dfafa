<template>
    <Modal
        v-model="visible"
        title="设置进出时间"
        :mask-closable="false"
        :closable="true"
        width="600"
        :footer-hide="true"
        @on-cancel="handleCancel">
        <Form ref="timeForm" :model="timeData" :rules="rules" :label-width="100" class="time-form">
            <Row :gutter="20">
                <Col :span="12">
                    <FormItem label="进入开始时间" prop="enterStartTime">
                        <TimePicker
                            v-model="timeData.enterStartTime"
                            type="time"
                            placeholder="请选择进入开始时间"
                            format="HH:mm:ss"
                            :editable="false"
                            style="width: 100%"
                            @on-change="validateEnterTimes" />
                    </FormItem>
                </Col>
                <Col :span="12">
                    <FormItem label="进入结束时间" prop="enterEndTime">
                        <TimePicker
                            v-model="timeData.enterEndTime"
                            type="time"
                            placeholder="请选择进入结束时间"
                            format="HH:mm:ss"
                            :editable="false"
                            style="width: 100%"
                            @on-change="validateEnterTimes" />
                    </FormItem>
                </Col>
            </Row>
            <Row :gutter="20" style="margin-top: 16px;">
                <Col :span="12">
                    <FormItem label="离开开始时间" prop="exitStartTime">
                        <TimePicker
                            v-model="timeData.exitStartTime"
                            type="time"
                            placeholder="请选择离开开始时间"
                            format="HH:mm:ss"
                            :editable="false"
                            style="width: 100%"
                            @on-change="validateExitTimes" />
                    </FormItem>
                </Col>
                <Col :span="12">
                    <FormItem label="离开结束时间" prop="exitEndTime">
                        <TimePicker
                            v-model="timeData.exitEndTime"
                            type="time"
                            placeholder="请选择离开结束时间"
                            format="HH:mm:ss"
                            :editable="false"
                            style="width: 100%"
                            @on-change="validateExitTimes" />
                    </FormItem>
                </Col>
            </Row>
            <div class="time-tips">
                <p><Icon type="ios-information-circle" /> 说明：</p>
                <p>• 进入结束时间必须大于进入开始时间</p>
                <p>• 离开结束时间必须大于离开开始时间</p>
                <p>• 时间格式为：时:分:秒（如：08:30:00）</p>
            </div>
        </Form>

        <!-- 自定义底部按钮 -->
        <div class="modal-footer">
            <Button @click="handleCancel">取消</Button>
            <Button type="primary" :loading="loading" @click="handleSubmit">确定</Button>
        </div>
    </Modal>
</template>

<script>
import { authorizationRecordService } from '@/api/safeManage/accessControlService.ts'

export default {
    name: 'AccessTimeModal',
    data() {
        return {
            visible: false,
            loading: false,
            recordId: null,
            timeData: {
                enterStartTime: '',
                enterEndTime: '',
                exitStartTime: '',
                exitEndTime: ''
            },
            rules: {
                enterStartTime: [
                    { required: true, message: '请选择进入开始时间', trigger: 'change' }
                ],
                enterEndTime: [
                    { required: true, message: '请选择进入结束时间', trigger: 'change' },
                    { validator: this.validateEnterEndTime, trigger: 'change' }
                ],
                exitStartTime: [
                    { required: true, message: '请选择离开开始时间', trigger: 'change' }
                ],
                exitEndTime: [
                    { required: true, message: '请选择离开结束时间', trigger: 'change' },
                    { validator: this.validateExitEndTime, trigger: 'change' }
                ]
            }
        }
    },
    methods: {
        // 显示弹窗
        show(record) {
            this.recordId = record.id
            this.timeData = {
                enterStartTime: record.enterStartTime || '',
                enterEndTime: record.enterEndTime || '',
                exitStartTime: record.exitStartTime || '',
                exitEndTime: record.exitEndTime || ''
            }
            this.visible = true
            this.$nextTick(() => {
                this.$refs.timeForm.resetFields()
            })
        },

        // 隐藏弹窗
        hide() {
            this.visible = false
            this.resetData()
        },

        // 重置数据
        resetData() {
            this.recordId = null
            this.timeData = {
                enterStartTime: '',
                enterEndTime: '',
                exitStartTime: '',
                exitEndTime: ''
            }
            this.loading = false
        },

        // 取消
        handleCancel() {
            this.hide()
        },

        // 提交
        handleSubmit() {
            this.$refs.timeForm.validate((valid) => {
                if (valid) {
                    this.loading = true
                    const params = {
                        id: this.recordId,
                        enterStartTime: this.timeData.enterStartTime,
                        enterEndTime: this.timeData.enterEndTime,
                        exitStartTime: this.timeData.exitStartTime,
                        exitEndTime: this.timeData.exitEndTime
                    }

                    authorizationRecordService.setAccessTimes(params)
                        .then((res) => {
                            if (res.success) {
                                this.$Message.success('设置成功')
                                this.$emit('success')
                                this.hide()
                            } else {
                                this.$Message.error(res.message || '设置失败')
                            }
                        })
                        .catch((err) => {
                            console.error('设置进出时间失败:', err)
                            this.$Message.error('设置失败，请重试')
                        })
                        .finally(() => {
                            this.loading = false
                        })
                } else {
                    this.$Message.error('请检查表单填写是否正确')
                }
            })
        },

        // 验证进入结束时间
        validateEnterEndTime(rule, value, callback) {
            if (value && this.timeData.enterStartTime) {
                if (this.compareTime(value, this.timeData.enterStartTime) <= 0) {
                    callback(new Error('进入结束时间必须大于进入开始时间'))
                } else {
                    callback()
                }
            } else {
                callback()
            }
        },

        // 验证离开结束时间
        validateExitEndTime(rule, value, callback) {
            if (value && this.timeData.exitStartTime) {
                if (this.compareTime(value, this.timeData.exitStartTime) <= 0) {
                    callback(new Error('离开结束时间必须大于离开开始时间'))
                } else {
                    callback()
                }
            } else {
                callback()
            }
        },

        // 比较时间大小
        compareTime(time1, time2) {
            if (!time1 || !time2) return 0
            const t1 = new Date(`2000-01-01 ${time1}`)
            const t2 = new Date(`2000-01-01 ${time2}`)
            return t1.getTime() - t2.getTime()
        },

        // 验证进入时间
        validateEnterTimes() {
            this.$nextTick(() => {
                if (this.timeData.enterEndTime) {
                    this.$refs.timeForm.validateField('enterEndTime')
                }
            })
        },

        // 验证离开时间
        validateExitTimes() {
            this.$nextTick(() => {
                if (this.timeData.exitEndTime) {
                    this.$refs.timeForm.validateField('exitEndTime')
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.time-form {
    padding: 8px 0;

    :deep(.ivu-form-item) {
        margin-bottom: 20px;
    }

    :deep(.ivu-form-item-label) {
        font-weight: 500;
        color: #333;
    }
}

.time-tips {
    margin-top: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #2d8cf0;

    p {
        margin: 0 0 6px 0;
        font-size: 13px;
        color: #666;
        line-height: 1.5;

        &:first-child {
            font-weight: 500;
            color: #2d8cf0;
            margin-bottom: 10px;
            font-size: 14px;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    .ivu-icon {
        margin-right: 6px;
        font-size: 14px;
    }
}

.modal-footer {
    text-align: right;
    padding: 16px 0 0 0;
    margin-top: 24px;
    border-top: 1px solid #e8eaec;

    .ivu-btn {
        margin-left: 8px;
        min-width: 80px;

        &:first-child {
            margin-left: 0;
        }
    }
}
</style>
