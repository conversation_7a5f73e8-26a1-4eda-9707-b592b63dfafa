<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>门禁管理</BreadcrumbItem>
        <BreadcrumbItem>门禁授权记录</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="门禁授权记录">
        <BaseForm inline :label-width="90" :model="searchObj" @handle-submit="search">
            <template #formitem>
                <FormItem label="人员姓名" prop="personNameKey">
                    <Input v-model="searchObj.personNameKey" :maxlength="20" placeholder="请输入人员姓名" clearable></Input>
                </FormItem>
                <FormItem label="证件号码" prop="personIdCardKey">
                    <Input v-model="searchObj.personIdCardKey" :maxlength="20" placeholder="请输入证件号码" clearable></Input>
                </FormItem>
                <FormItem label="门禁编码" prop="accessControlCodeKey">
                    <Input v-model="searchObj.accessControlCodeKey" :maxlength="20" placeholder="请输入门禁编码" clearable></Input>
                </FormItem>
                <FormItem label="门禁名称" prop="accessControlNameKey">
                    <Input v-model="searchObj.accessControlNameKey" :maxlength="20" placeholder="请输入门禁名称" clearable></Input>
                </FormItem>
            </template>
        </BaseForm>
        <btn-card>
            <Button type="primary" icon="md-add" @click="add" v-auth="'accessControl:authorizationRecord:add'">
                新增
            </Button>
            <Button type="error" icon="md-trash" @click="batchDelete" v-auth="'accessControl:authorizationRecord:revoke'" :disabled="m.selectList.length === 0">
                批量删除
            </Button>
        </btn-card>
        <base-table
            ref="listCom"
            :columns="columns"
            :url="tableUrl"
            @on-selection-change="selectionChange">
                <template #action="{ row }">
                    <link-btn size="small" @click="toDetail(row)">
                        详情
                    </link-btn>
                    <link-btn size="small" @click="setAccessTimes(row)" v-auth="'accessControl:authorizationRecord:timeLimit'">
                        设置进出时间
                    </link-btn>
                    <link-btn size="small" @click="revokeAuth(row)" v-auth="'accessControl:authorizationRecord:revoke'">
                        撤销
                    </link-btn>
                </template>
            </base-table>
    </ContentCard>
</template>

<script>
export default {
    name: 'AuthorizationRecordList'
}
</script>

<script setup>
import { getCurrentInstance, onMounted, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { authorizationRecordService } from '@/api/safeManage/accessControlService.ts'
const that = getCurrentInstance()?.appContext.config.globalProperties
const router = useRouter()

const tableUrl = '/accessAuth/list'
const columns = ref([
    { type: 'selection', width: 50, align: 'center' },
    { title: '人员姓名', key: 'personName', tooltip: true },
    { title: '证件号码', key: 'certificateNo', tooltip: true },
    { title: '手机号', key: 'phoneNo', tooltip: true },
    { title: '门禁编码', key: 'accessControlCode', tooltip: true },
    { title: '门禁名称', key: 'deviceName', tooltip: true },
    {
        title: '进入授权时间',
        key: 'enterTime',
        tooltip: true,
        render: (h, params) => {
            const { enterStartTime, enterEndTime } = params.row
            if (enterStartTime && enterEndTime) {
                return h('span', `${enterStartTime}-${enterEndTime}`)
            } else if (enterStartTime || enterEndTime) {
                return h('span', enterStartTime || enterEndTime)
            }
            return h('span', '--')
        }
    },
    {
        title: '离开授权时间',
        key: 'exitTime',
        tooltip: true,
        render: (h, params) => {
            const { exitStartTime, exitEndTime } = params.row
            if (exitStartTime && exitEndTime) {
                return h('span', `${exitStartTime}-${exitEndTime}`)
            } else if (exitStartTime || exitEndTime) {
                return h('span', exitStartTime || exitEndTime)
            }
            return h('span', '--')
        }
    },
    { title: '创建时间', key: 'createTime', tooltip: true },
    { title: '操作', slot: 'action', width: 200, align: 'center' }
])
const searchObj = ref({
    personNameKey: '', // 人员姓名
    personIdCardKey: '', // 证件号码
    accessControlCodeKey: '', // 门禁编码
    accessControlNameKey: '' // 门禁名称
})
const m = reactive({
    unitList: [],
    selectList: [] // 表格已选数据
})
const revokeReason = ref('') // 撤销原因
onMounted(() => { search() })
function selectionChange(list) {
    m.selectList = list || []
}
const listCom = ref()
function search() {
    listCom.value.search(searchObj.value)
}

function add() {
    router.push({
        path: '/accessControl/authorizationRecordAdd'
    })
}
function toDetail(row) {
    router.push({
        path: '/accessControl/authorizationRecordDetail',
        query: {
            id: row.id
        }
    })
}

function revokeAuth(row) {
    that.$Modal.confirm({
        title: '撤销授权',
        render: (h) => {
            return h('div', [
                h('p', '请输入撤销原因：'),
                h('Input', {
                    props: {
                        type: 'textarea',
                        rows: 3,
                        placeholder: '请输入撤销原因'
                    },
                    on: {
                        input: (val) => {
                            revokeReason.value = val
                        }
                    }
                })
            ])
        },
        onOk: () => {
            if (!revokeReason.value.trim()) {
                that.$Message.error('请输入撤销原因')
                return false
            }
            authorizationRecordService.revoke(row.id, revokeReason.value)
            .then((res) => {
                if (res.success) {
                    that.$Message.success('撤销成功')
                    search()
                    revokeReason.value = ''
                }
            })
        },
        onCancel: () => {
            revokeReason.value = ''
        }
    })
}


function batchDelete() {
    if (m.selectList.length === 0) {
        that.$Message.warning('请选择要删除的记录')
        return
    }
    that.$Modal.confirm({
        title: '提示',
        content: `您确定要删除选中的 ${m.selectList.length} 条授权记录吗？`,
        onOk: () => {
            const ids = m.selectList.map(item => item.id)
            authorizationRecordService.delete(ids)
            .then((res) => {
                if (res.success) {
                    that.$Message.success('删除成功')
                    search()
                    m.selectList = []
                }
            })
        }
    })
}

function setAccessTimes(row) {
    // 这里可以打开设置进出时间的弹窗
    that.$Modal.info({
        title: '设置进出时间',
        content: '设置进出时间功能开发中...',
        onOk: () => {
            // TODO: 实现设置进出时间功能
            console.log('设置进出时间:', row)
        }
    })
}

</script>

<style lang="less" scoped>

</style>
