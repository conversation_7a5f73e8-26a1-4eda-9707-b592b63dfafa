<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>门禁管理</BreadcrumbItem>
            <BreadcrumbItem to="/accessControl/accessHistory">进出门历史</BreadcrumbItem>
            <BreadcrumbItem>详情</BreadcrumbItem>
        </BreadcrumbCustom>
        <detailCard title="进出门历史详情" :is-back-btn="true" @on-back="backPage">
            <div class="detail-content">
                <Row :gutter="24">
                    <Col :span="8">
                        <div class="detail-item">
                            <label>人员姓名：</label>
                            <span>{{ detailData.personName || '--' }}</span>
                        </div>
                    </Col>
                    <Col :span="8">
                        <div class="detail-item">
                            <label>证件号码：</label>
                            <span>{{ detailData.personIdCard || '--' }}</span>
                        </div>
                    </Col>
                    <Col :span="8">
                        <div class="detail-item">
                            <label>手机号：</label>
                            <span>{{ detailData.personPhone || '--' }}</span>
                        </div>
                    </Col>
                </Row>
                <Row :gutter="24">
                    <Col :span="8">
                        <div class="detail-item">
                            <label>门禁编码：</label>
                            <span>{{ detailData.accessControlCode || '--' }}</span>
                        </div>
                    </Col>
                    <Col :span="8">
                        <div class="detail-item">
                            <label>门禁名称：</label>
                            <span>{{ detailData.accessControlName || '--' }}</span>
                        </div>
                    </Col>
                    <Col :span="8">
                        <div class="detail-item">
                            <label>进出类型：</label>
                            <Tag v-if="detailData.accessType === 1" color="green">进入</Tag>
                            <Tag v-else-if="detailData.accessType === 2" color="orange">外出</Tag>
                            <span v-else>--</span>
                        </div>
                    </Col>
                </Row>
                <Row :gutter="24">
                    <Col :span="8">
                        <div class="detail-item">
                            <label>进出时间：</label>
                            <span>{{ detailData.accessTime || '--' }}</span>
                        </div>
                    </Col>
                    <Col :span="8">
                        <div class="detail-item">
                            <label>创建时间：</label>
                            <span>{{ detailData.createTime || '--' }}</span>
                        </div>
                    </Col>
                    <Col :span="8">
                        <div class="detail-item">
                            <label>所在街道：</label>
                            <span>{{ detailData.szjd || '--' }}</span>
                        </div>
                    </Col>
                </Row>
                <Row :gutter="24">
                    <Col :span="8">
                        <div class="detail-item">
                            <label>所在社区：</label>
                            <span>{{ detailData.szsq || '--' }}</span>
                        </div>
                    </Col>
                    <Col :span="8">
                        <div class="detail-item">
                            <label>所在单元网格：</label>
                            <span>{{ detailData.szdywg || '--' }}</span>
                        </div>
                    </Col>
                </Row>
                <Row :gutter="24" v-if="detailData.remark">
                    <Col :span="24">
                        <div class="detail-item">
                            <label>备注：</label>
                            <span>{{ detailData.remark || '--' }}</span>
                        </div>
                    </Col>
                </Row>
            </div>
        </detailCard>
    </div>
</template>

<script>
import { getCurrentInstance, onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { accessHistoryService } from '@/api/safeManage/accessControlService.ts'

export default {
    name: 'AccessHistoryDetail',
    setup() {
        const that = getCurrentInstance()?.appContext.config.globalProperties
        const router = useRouter()
        const route = useRoute()
        
        const detailData = ref({})
        const loading = ref(false)
        
        onMounted(() => {
            const id = route.query.id
            if (id) {
                getDetail(id)
            }
        })
        
        function getDetail(id) {
            loading.value = true
            accessHistoryService.getById(id)
                .then((res) => {
                    if (res.success) {
                        detailData.value = res.data || {}
                    } else {
                        that.$Message.error(res.message || '获取详情失败')
                    }
                })
                .catch((err) => {
                    console.error('获取进出门历史详情失败:', err)
                    that.$Message.error('获取详情失败，请重试')
                })
                .finally(() => {
                    loading.value = false
                })
        }
        
        function backPage() {
            router.back()
        }
        
        return {
            detailData,
            loading,
            backPage
        }
    }
}
</script>

<style lang="less" scoped>
.detail-content {
    padding: 20px 0;
    
    .detail-item {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        
        label {
            font-weight: 500;
            color: #333;
            min-width: 100px;
            margin-right: 8px;
        }
        
        span {
            color: #666;
            word-break: break-all;
        }
    }
}
</style>
