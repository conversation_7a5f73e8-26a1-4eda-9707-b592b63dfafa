<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>门禁管理</BreadcrumbItem>
        <BreadcrumbItem>进出门历史</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="进出门历史">
        <BaseForm inline :label-width="90" :model="searchObj" @handle-submit="search">
            <template #formitem>
                <FormItem label="人员姓名" prop="personNameKey">
                    <Input v-model="searchObj.personNameKey" :maxlength="20" placeholder="请输入人员姓名" clearable></Input>
                </FormItem>
                <FormItem label="证件号码" prop="personIdCardKey">
                    <Input v-model="searchObj.personIdCardKey" :maxlength="20" placeholder="请输入证件号码" clearable></Input>
                </FormItem>
                <FormItem label="手机号" prop="personPhoneKey">
                    <Input v-model="searchObj.personPhoneKey" :maxlength="20" placeholder="请输入手机号" clearable></Input>
                </FormItem>
                <FormItem label="门禁编码" prop="accessControlCodeKey">
                    <Input v-model="searchObj.accessControlCodeKey" :maxlength="20" placeholder="请输入门禁编码" clearable></Input>
                </FormItem>
                <FormItem label="门禁名称" prop="accessControlNameKey">
                    <Input v-model="searchObj.accessControlNameKey" :maxlength="20" placeholder="请输入门禁名称" clearable></Input>
                </FormItem>
                <FormItem label="进出类型" prop="accessType">
                    <Select v-model="searchObj.accessType" placeholder="请选择进出类型" clearable>
                        <Option value="1">进入</Option>
                        <Option value="2">外出</Option>
                    </Select>
                </FormItem>
                <FormItem label="进出时间" prop="timeRange">
                    <DatePicker
                        v-model="searchObj.timeRange"
                        type="datetimerange"
                        placeholder="请选择时间范围"
                        format="yyyy-MM-dd HH:mm:ss"
                        :editable="false"
                        style="width: 100%"
                        @on-change="onTimeRangeChange" />
                </FormItem>
            </template>
        </BaseForm>
        <base-table
            ref="listCom"
            :columns="columns"
            :url="tableUrl"
            @on-selection-change="selectionChange">
                <template #action="{ row }">
                    <link-btn size="small" @click="toDetail(row)">
                        详情
                    </link-btn>
                </template>
            </base-table>
    </ContentCard>
</template>

<script>
export default {
    name: 'AccessHistoryList'
}
</script>

<script setup>
import { getCurrentInstance, onMounted, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
const that = getCurrentInstance()?.appContext.config.globalProperties
const router = useRouter()

const tableUrl = '/accessHistory/list'
const columns = ref([
    { title: '人员姓名', key: 'personName', tooltip: true },
    { title: '证件号码', key: 'personIdCard', tooltip: true },
    { title: '手机号', key: 'personPhone', tooltip: true },
    { title: '门禁编码', key: 'accessControlCode', tooltip: true },
    { title: '门禁名称', key: 'accessControlName', tooltip: true },
    {
        title: '进出类型',
        key: 'accessType',
        tooltip: true,
        render: (h, params) => {
            const type = params.row.accessType
            if (type === 1) {
                return h('Tag', { props: { color: 'green' } }, '进入')
            } else if (type === 2) {
                return h('Tag', { props: { color: 'orange' } }, '外出')
            }
            return h('span', '--')
        }
    },
    { title: '进出时间', key: 'accessTime', tooltip: true },
    { title: '创建时间', key: 'createTime', tooltip: true },
    { title: '操作', slot: 'action', width: 120, align: 'center' }
])

const searchObj = ref({
    personNameKey: '', // 人员姓名
    personIdCardKey: '', // 证件号码
    personPhoneKey: '', // 手机号
    accessControlCodeKey: '', // 门禁编码
    accessControlNameKey: '', // 门禁名称
    accessType: '', // 进出类型
    timeRange: [], // 时间范围
    startTime: '', // 开始时间
    endTime: '' // 结束时间
})

const m = reactive({
    selectList: [] // 表格已选数据
})

onMounted(() => { search() })

function selectionChange(list) {
    m.selectList = list || []
}

const listCom = ref()
function search() {
    listCom.value.search(searchObj.value)
}

function toDetail(row) {
    router.push({
        path: '/accessControl/accessHistoryDetail',
        query: {
            id: row.id
        }
    })
}

// 时间范围变化处理
function onTimeRangeChange(timeRange) {
    if (timeRange && timeRange.length === 2) {
        searchObj.value.startTime = that.$Util.formatDate(timeRange[0], 'YYYY-MM-DD HH:mm:ss')
        searchObj.value.endTime = that.$Util.formatDate(timeRange[1], 'YYYY-MM-DD HH:mm:ss')
    } else {
        searchObj.value.startTime = ''
        searchObj.value.endTime = ''
    }
}

</script>

<style lang="less" scoped>

</style>
