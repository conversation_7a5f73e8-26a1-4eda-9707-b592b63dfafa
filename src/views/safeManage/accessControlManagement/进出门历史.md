---
title: 安全守卫
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 安全守卫

Base URLs:

# Authentication

# 进出门历史管理

## POST 根据条件，分页(不分页)查询

POST /accessHistory/list

> Body 请求参数

```json
{
  "page": {
    "records": [
      {
        "id": 0,
        "accessControlId": 0,
        "accessControlCode": "string",
        "personId": 0,
        "personName": "string",
        "personIdCard": "string",
        "personPhone": "string",
        "accessType": 0,
        "accessTime": "string",
        "createTime": "string",
        "remark": "string",
        "deleted": 0,
        "ids": [
          0
        ],
        "personNameKey": "string",
        "personIdCardKey": "string",
        "personPhoneKey": "string",
        "accessControlCodeKey": "string",
        "accessControlNameKey": "string",
        "startTime": "string",
        "endTime": "string",
        "szjd": "string",
        "szsq": "string",
        "szdywg": "string",
        "accessControlName": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": true,
    "isSearchCount": true
  },
  "customQueryParams": {
    "id": 0,
    "accessControlId": 0,
    "accessControlCode": "string",
    "personId": 0,
    "personName": "string",
    "personIdCard": "string",
    "personPhone": "string",
    "accessType": 0,
    "accessTime": "string",
    "createTime": "string",
    "remark": "string",
    "deleted": 0,
    "ids": [
      0
    ],
    "personNameKey": "string",
    "personIdCardKey": "string",
    "personPhoneKey": "string",
    "accessControlCodeKey": "string",
    "accessControlNameKey": "string",
    "startTime": "string",
    "endTime": "string",
    "szjd": "string",
    "szsq": "string",
    "szdywg": "string",
    "accessControlName": "string"
  },
  "sorts": [
    {
      "field": "string",
      "sortRule": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[RequestModelAccessHistoryVo](#schemarequestmodelaccesshistoryvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## GET 根据id查询进出门历史详情

GET /accessHistory/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

# 数据模型

<h2 id="tocS_RestMessage">RestMessage</h2>

<a id="schemarestmessage"></a>
<a id="schema_RestMessage"></a>
<a id="tocSrestmessage"></a>
<a id="tocsrestmessage"></a>

```json
{
  "success": true,
  "code": "string",
  "level": "string",
  "message": "string",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||成功标识|
|code|string|false|none||状态码|
|level|string|false|none||消息级别|
|message|string|false|none||消息内容|
|data|object|false|none||数据|

<h2 id="tocS_"></h2>

<a id="schema"></a>
<a id="schema_"></a>
<a id="tocS"></a>
<a id="tocs"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_RequestModelAccessHistoryVo">RequestModelAccessHistoryVo</h2>

<a id="schemarequestmodelaccesshistoryvo"></a>
<a id="schema_RequestModelAccessHistoryVo"></a>
<a id="tocSrequestmodelaccesshistoryvo"></a>
<a id="tocsrequestmodelaccesshistoryvo"></a>

```json
{
  "page": {
    "records": [
      {
        "id": 0,
        "accessControlId": 0,
        "accessControlCode": "string",
        "personId": 0,
        "personName": "string",
        "personIdCard": "string",
        "personPhone": "string",
        "accessType": 0,
        "accessTime": "string",
        "createTime": "string",
        "remark": "string",
        "deleted": 0,
        "ids": [
          0
        ],
        "personNameKey": "string",
        "personIdCardKey": "string",
        "personPhoneKey": "string",
        "accessControlCodeKey": "string",
        "accessControlNameKey": "string",
        "startTime": "string",
        "endTime": "string",
        "szjd": "string",
        "szsq": "string",
        "szdywg": "string",
        "accessControlName": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": true,
    "isSearchCount": true
  },
  "customQueryParams": {
    "id": 0,
    "accessControlId": 0,
    "accessControlCode": "string",
    "personId": 0,
    "personName": "string",
    "personIdCard": "string",
    "personPhone": "string",
    "accessType": 0,
    "accessTime": "string",
    "createTime": "string",
    "remark": "string",
    "deleted": 0,
    "ids": [
      0
    ],
    "personNameKey": "string",
    "personIdCardKey": "string",
    "personPhoneKey": "string",
    "accessControlCodeKey": "string",
    "accessControlNameKey": "string",
    "startTime": "string",
    "endTime": "string",
    "szjd": "string",
    "szsq": "string",
    "szdywg": "string",
    "accessControlName": "string"
  },
  "sorts": [
    {
      "field": "string",
      "sortRule": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|page|[PageAccessHistoryVo](#schemapageaccesshistoryvo)|false|none||分页参数|
|customQueryParams|[AccessHistoryVo](#schemaaccesshistoryvo)|false|none||业务相关的查询参数|
|sorts|[[SortModel](#schemasortmodel)]|false|none||查询时排序参数|

<h2 id="tocS_SortModel">SortModel</h2>

<a id="schemasortmodel"></a>
<a id="schema_SortModel"></a>
<a id="tocSsortmodel"></a>
<a id="tocssortmodel"></a>

```json
{
  "field": "string",
  "sortRule": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|field|string|false|none||排序的字段|
|sortRule|string|false|none||排序规则 ASC / DESC|

<h2 id="tocS_AccessHistoryVo">AccessHistoryVo</h2>

<a id="schemaaccesshistoryvo"></a>
<a id="schema_AccessHistoryVo"></a>
<a id="tocSaccesshistoryvo"></a>
<a id="tocsaccesshistoryvo"></a>

```json
{
  "id": 0,
  "accessControlId": 0,
  "accessControlCode": "string",
  "personId": 0,
  "personName": "string",
  "personIdCard": "string",
  "personPhone": "string",
  "accessType": 0,
  "accessTime": "string",
  "createTime": "string",
  "remark": "string",
  "deleted": 0,
  "ids": [
    0
  ],
  "personNameKey": "string",
  "personIdCardKey": "string",
  "personPhoneKey": "string",
  "accessControlCodeKey": "string",
  "accessControlNameKey": "string",
  "startTime": "string",
  "endTime": "string",
  "szjd": "string",
  "szsq": "string",
  "szdywg": "string",
  "accessControlName": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键|
|accessControlId|integer(int64)|false|none||门禁设备ID|
|accessControlCode|string|false|none||门禁编码|
|personId|integer(int64)|false|none||人员ID|
|personName|string|false|none||人员姓名|
|personIdCard|string|false|none||人员身份证号|
|personPhone|string|false|none||人员手机号|
|accessType|integer|false|none||进出类型(1:进入,2:外出)|
|accessTime|string|false|none||进出时间|
|createTime|string|false|none||创建时间|
|remark|string|false|none||备注|
|deleted|integer|false|none||是否删除，0未删除，1已删除|
|ids|[integer]|false|none||none|
|personNameKey|string|false|none||人员姓名模糊查询|
|personIdCardKey|string|false|none||身份证号模糊查询|
|personPhoneKey|string|false|none||手机号模糊查询|
|accessControlCodeKey|string|false|none||门禁编码模糊查询|
|accessControlNameKey|string|false|none||门禁名称模糊查询|
|startTime|string|false|none||开始时间|
|endTime|string|false|none||结束时间|
|szjd|string|false|none||所在街道|
|szsq|string|false|none||所在社区|
|szdywg|string|false|none||所在单元网格|
|accessControlName|string|false|none||门禁名称|

<h2 id="tocS_PageAccessHistoryVo">PageAccessHistoryVo</h2>

<a id="schemapageaccesshistoryvo"></a>
<a id="schema_PageAccessHistoryVo"></a>
<a id="tocSpageaccesshistoryvo"></a>
<a id="tocspageaccesshistoryvo"></a>

```json
{
  "records": [
    {
      "id": 0,
      "accessControlId": 0,
      "accessControlCode": "string",
      "personId": 0,
      "personName": "string",
      "personIdCard": "string",
      "personPhone": "string",
      "accessType": 0,
      "accessTime": "string",
      "createTime": "string",
      "remark": "string",
      "deleted": 0,
      "ids": [
        0
      ],
      "personNameKey": "string",
      "personIdCardKey": "string",
      "personPhoneKey": "string",
      "accessControlCodeKey": "string",
      "accessControlNameKey": "string",
      "startTime": "string",
      "endTime": "string",
      "szjd": "string",
      "szsq": "string",
      "szdywg": "string",
      "accessControlName": "string"
    }
  ],
  "total": 0,
  "size": 0,
  "current": 0,
  "orders": [
    {
      "column": "string",
      "asc": true
    }
  ],
  "optimizeCountSql": true,
  "isSearchCount": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|records|[[AccessHistoryVo](#schemaaccesshistoryvo)]|false|none||none|
|total|integer(int64)|false|none||none|
|size|integer(int64)|false|none||none|
|current|integer(int64)|false|none||none|
|orders|[[OrderItem](#schemaorderitem)]|false|none||none|
|optimizeCountSql|boolean|false|none||none|
|isSearchCount|boolean|false|none||none|

<h2 id="tocS_OrderItem">OrderItem</h2>

<a id="schemaorderitem"></a>
<a id="schema_OrderItem"></a>
<a id="tocSorderitem"></a>
<a id="tocsorderitem"></a>

```json
{
  "column": "string",
  "asc": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|column|string|false|none||none|
|asc|boolean|false|none||none|

